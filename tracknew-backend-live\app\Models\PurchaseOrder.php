<?php

namespace App\Models;

use Eloquent as Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @OA\Schema(
 *      schema="PurchaseOrder",
 *      required={""},
 *      @OA\Property(
 *          property="id",
 *          description="id",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="purchase_order",
 *          description="purchase_order",
 *          readOnly=false,
 *          nullable=false,
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="purchase_order_date",
 *          description="purchase_order_date",
 *          readOnly=false,
 *          nullable=false,
 *          type="string",
 *          format="date"
 *      ),
 *      @OA\Property(
 *          property="paid",
 *          description="paid",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="total",
 *          description="total",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="warehouse_id",
 *          description="warehouse_id",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="company_id",
 *          description="company_id",
 *          readOnly=false,
 *          nullable=false,
 *          type="string"
 *      ),
 *      @OA\Property(
 *          property="supplier_id",
 *          description="supplier_id",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="created_at",
 *          description="created_at",
 *          readOnly=false,
 *          nullable=false,
 *          type="string",
 *          format="date-time"
 *      ),
 *      @OA\Property(
 *          property="updated_at",
 *          description="updated_at",
 *          readOnly=false,
 *          nullable=false,
 *          type="string",
 *          format="date-time"
 *      )
 * )
 */
class PurchaseOrder extends Model
{
    use SoftDeletes;

    use HasFactory;

    public $table = 'purchaseorder';
    

    protected $dates = ['deleted_at'];



    public $fillable = [
        'purchase_order',
        'purchase_order_date',
        'paid',
        'total',
        'sales_amount',
        'warehouse_id',
        'company_id',
        'supplier_id',
        'due_interval',
        'sos',
        'payment_type',
        'return_amount',
        'balance_amount',
      	'shipping',
      	'shipping_type',
      	'discount',
      	'discount_type',
      	'cod'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'purchase_order' => 'string',
        'purchase_order_date' => 'date',
        'paid' => 'integer',
        'total' => 'integer',
        'sales_amount' => 'integer',
        'warehouse_id' => 'integer',
        'company_id' => 'string',
        'supplier_id' => 'integer',
        'return_amount' => 'integer',
        'balance_amount' => 'integer',
        'payment_type' => 'string',
        'due_interval' => 'string',
        'sos'=> 'string',
      	'shipping' => 'float',
      	'shipping_type' => 'string',
      	'discount' => 'float',
      	'discount_type' => 'string',
      	'cod' => 'string'
    ];

    /**
     * Validation rules
     *
     * @var array
     */
    public static $rules = [
        
    ];

     /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     **/
    public function puchaseitems()
    {
        return $this->hasMany(PurchaseOrderItem::class, 'purchaseorder_id', 'id');
    }
    
      /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     **/
    public function puchasePayments()
    {
        return $this->hasMany(PurchaseOrderPayments::class, 'purchaseorder_id', 'id');
    }

     /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     **/
    public function barcodes()
    {
        return $this->hasMany(PurchaseOrderPayments::class, 'id', 'purchaseorder_id');
    }
    
    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id', 'id');
    }

    public function purchaseOrderItems()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    
}
