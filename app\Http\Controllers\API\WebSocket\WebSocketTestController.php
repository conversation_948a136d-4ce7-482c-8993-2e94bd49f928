<?php

namespace App\Http\Controllers\API\WebSocket;

use App\Http\Controllers\AppBaseController;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

// Import all WebSocket events
use App\Events\Service\ServiceCreated;
use App\Events\Service\ServiceStatusUpdated;
use App\Events\Service\ServiceAssigned;
use App\Events\Service\ServiceOTPVerified;
use App\Events\Dashboard\DashboardMetricsUpdated;
use App\Events\Payment\PaymentReceived;
use App\Events\Payment\InvoiceGenerated;
use App\Events\AMC\AMCRenewalDue;
use App\Events\AMC\AMCServiceScheduled;
use App\Events\User\UserNotificationReceived;
use App\Events\User\MessageSent;
use App\Events\User\UserPresenceUpdated;
use App\Events\User\UserTypingIndicator;

/**
 * WebSocket Test Controller
 * 
 * This controller provides testing endpoints for WebSocket functionality.
 * It allows triggering various WebSocket events for testing purposes.
 * 
 * ⚠️ WARNING: This controller is for TESTING PURPOSES ONLY
 * Do not use in production environment.
 */
class WebSocketTestController extends AppBaseController
{
    /**
     * Get WebSocket test dashboard
     * 
     * @return JsonResponse
     */
    public function dashboard(): JsonResponse
    {
        $testData = [
            'websocket_enabled' => config('websockets.enabled', false),
            'websocket_host' => config('websockets.host', '127.0.0.1'),
            'websocket_port' => config('websockets.port', 6001),
            'available_events' => [
                'service_events' => [
                    'ServiceCreated',
                    'ServiceStatusUpdated', 
                    'ServiceAssigned',
                    'ServiceOTPVerified'
                ],
                'dashboard_events' => [
                    'DashboardMetricsUpdated'
                ],
                'payment_events' => [
                    'PaymentReceived',
                    'InvoiceGenerated'
                ],
                'amc_events' => [
                    'AMCRenewalDue',
                    'AMCServiceScheduled'
                ],
                'communication_events' => [
                    'UserNotificationReceived',
                    'MessageSent',
                    'UserPresenceUpdated',
                    'UserTypingIndicator'
                ]
            ],
            'test_endpoints' => [
                'POST /api/websocket/test/service-created',
                'POST /api/websocket/test/service-status-updated',
                'POST /api/websocket/test/service-assigned',
                'POST /api/websocket/test/service-otp-verified',
                'POST /api/websocket/test/dashboard-metrics',
                'POST /api/websocket/test/payment-received',
                'POST /api/websocket/test/invoice-generated',
                'POST /api/websocket/test/amc-renewal-due',
                'POST /api/websocket/test/amc-service-scheduled',
                'POST /api/websocket/test/user-notification',
                'POST /api/websocket/test/message-sent',
                'POST /api/websocket/test/user-presence',
                'POST /api/websocket/test/typing-indicator',
                'POST /api/websocket/test/bulk-events'
            ]
        ];

        return $this->sendResponse($testData, 'WebSocket test dashboard data retrieved successfully');
    }

    /**
     * Test Service Created Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testServiceCreated(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'service_id' => 'required|integer',
                'service_name' => 'required|string',
                'customer_name' => 'required|string',
                'priority' => 'string|in:low,medium,high,urgent',
                'created_by' => 'string'
            ]);

            $eventData = array_merge([
                'service_id' => $data['service_id'],
                'service_name' => $data['service_name'],
                'customer_name' => $data['customer_name'],
                'priority' => $data['priority'] ?? 'medium',
                'created_by' => $data['created_by'] ?? 'Test User',
                'created_at' => now()->toISOString(),
                'estimated_completion' => now()->addHours(4)->toISOString(),
                'test_event' => true
            ], $data);

            event(new ServiceCreated($eventData, $data['company_id']));

            Log::info('WebSocket Test: ServiceCreated event triggered', $eventData);

            return $this->sendResponse($eventData, 'ServiceCreated event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: ServiceCreated', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger ServiceCreated event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Service Status Updated Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testServiceStatusUpdated(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'service_id' => 'required|integer',
                'old_status' => 'required|string',
                'new_status' => 'required|string',
                'updated_by' => 'string',
                'notes' => 'string'
            ]);

            $eventData = array_merge([
                'service_id' => $data['service_id'],
                'old_status' => $data['old_status'],
                'new_status' => $data['new_status'],
                'updated_by' => $data['updated_by'] ?? 'Test Technician',
                'notes' => $data['notes'] ?? 'Status updated via test endpoint',
                'updated_at' => now()->toISOString(),
                'test_event' => true
            ], $data);

            event(new ServiceStatusUpdated($eventData, $data['company_id']));

            Log::info('WebSocket Test: ServiceStatusUpdated event triggered', $eventData);

            return $this->sendResponse($eventData, 'ServiceStatusUpdated event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: ServiceStatusUpdated', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger ServiceStatusUpdated event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Service Assigned Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testServiceAssigned(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'service_id' => 'required|integer',
                'technician_id' => 'required|integer',
                'technician_name' => 'required|string',
                'assigned_by' => 'string',
                'assignment_notes' => 'string'
            ]);

            $eventData = array_merge([
                'service_id' => $data['service_id'],
                'technician_id' => $data['technician_id'],
                'technician_name' => $data['technician_name'],
                'assigned_by' => $data['assigned_by'] ?? 'Test Manager',
                'assignment_notes' => $data['assignment_notes'] ?? 'Service assigned via test endpoint',
                'assigned_at' => now()->toISOString(),
                'expected_start' => now()->addHour()->toISOString(),
                'test_event' => true
            ], $data);

            event(new ServiceAssigned($eventData, $data['company_id'], $data['technician_id']));

            Log::info('WebSocket Test: ServiceAssigned event triggered', $eventData);

            return $this->sendResponse($eventData, 'ServiceAssigned event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: ServiceAssigned', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger ServiceAssigned event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Dashboard Metrics Updated Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testDashboardMetrics(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string'
            ]);

            $eventData = [
                'services' => [
                    'total' => rand(100, 500),
                    'completed_today' => rand(10, 50),
                    'pending' => rand(20, 80),
                    'in_progress' => rand(5, 25),
                    'completion_rate' => rand(75, 95) . '%'
                ],
                'financial' => [
                    'revenue_today' => rand(10000, 50000),
                    'revenue_month' => rand(200000, 800000),
                    'pending_payments' => rand(5000, 25000),
                    'collection_rate' => rand(80, 95) . '%'
                ],
                'customers' => [
                    'total_customers' => rand(500, 2000),
                    'new_today' => rand(2, 15),
                    'satisfaction_score' => rand(4.2, 4.9),
                    'repeat_customers' => rand(60, 85) . '%'
                ],
                'team' => [
                    'active_technicians' => rand(8, 25),
                    'available_now' => rand(3, 12),
                    'average_rating' => rand(4.0, 4.8),
                    'productivity_score' => rand(75, 95) . '%'
                ],
                'updated_at' => now()->toISOString(),
                'test_event' => true
            ];

            event(new DashboardMetricsUpdated($eventData, $data['company_id']));

            Log::info('WebSocket Test: DashboardMetricsUpdated event triggered', $eventData);

            return $this->sendResponse($eventData, 'DashboardMetricsUpdated event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: DashboardMetricsUpdated', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger DashboardMetricsUpdated event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Payment Received Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testPaymentReceived(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'payment_id' => 'required|integer',
                'service_id' => 'integer',
                'amount' => 'required|numeric',
                'customer_name' => 'required|string',
                'payment_method' => 'string'
            ]);

            $eventData = array_merge([
                'payment_id' => $data['payment_id'],
                'service_id' => $data['service_id'] ?? null,
                'amount' => $data['amount'],
                'customer_name' => $data['customer_name'],
                'payment_method' => $data['payment_method'] ?? 'credit_card',
                'transaction_id' => 'TXN' . time() . rand(1000, 9999),
                'payment_status' => 'completed',
                'received_at' => now()->toISOString(),
                'test_event' => true
            ], $data);

            event(new PaymentReceived($eventData, $data['company_id']));

            Log::info('WebSocket Test: PaymentReceived event triggered', $eventData);

            return $this->sendResponse($eventData, 'PaymentReceived event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: PaymentReceived', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger PaymentReceived event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test User Notification Event
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function testUserNotification(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'user_id' => 'required|integer',
                'title' => 'required|string',
                'message' => 'required|string',
                'priority' => 'string|in:low,medium,high,urgent',
                'action_required' => 'boolean'
            ]);

            $eventData = array_merge([
                'notification_id' => time() . rand(100, 999),
                'user_id' => $data['user_id'],
                'title' => $data['title'],
                'message' => $data['message'],
                'priority' => $data['priority'] ?? 'medium',
                'action_required' => $data['action_required'] ?? false,
                'created_at' => now()->toISOString(),
                'expires_at' => now()->addDays(7)->toISOString(),
                'test_event' => true
            ], $data);

            event(new UserNotificationReceived($eventData, $data['company_id'], $data['user_id']));

            Log::info('WebSocket Test: UserNotificationReceived event triggered', $eventData);

            return $this->sendResponse($eventData, 'UserNotificationReceived event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: UserNotificationReceived', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger UserNotificationReceived event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Message Sent Event
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testMessageSent(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'sender_id' => 'required|integer',
                'recipient_id' => 'required|integer',
                'message' => 'required|string',
                'conversation_id' => 'string'
            ]);

            $eventData = array_merge([
                'message_id' => time() . rand(100, 999),
                'sender_id' => $data['sender_id'],
                'recipient_id' => $data['recipient_id'],
                'message' => $data['message'],
                'conversation_id' => $data['conversation_id'] ?? 'conv_' . $data['sender_id'] . '_' . $data['recipient_id'],
                'message_type' => 'text',
                'sent_at' => now()->toISOString(),
                'test_event' => true
            ], $data);

            event(new MessageSent($eventData, $data['company_id'], $data['sender_id'], $data['recipient_id']));

            Log::info('WebSocket Test: MessageSent event triggered', $eventData);

            return $this->sendResponse($eventData, 'MessageSent event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: MessageSent', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger MessageSent event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test User Presence Updated Event
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testUserPresence(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'user_id' => 'required|integer',
                'status' => 'required|string|in:online,offline,away,busy',
                'activity' => 'string',
                'location' => 'string'
            ]);

            $eventData = array_merge([
                'user_id' => $data['user_id'],
                'status' => $data['status'],
                'activity' => $data['activity'] ?? 'Available',
                'location' => $data['location'] ?? 'Office',
                'last_seen' => now()->toISOString(),
                'device_info' => 'Test Device',
                'test_event' => true
            ], $data);

            event(new UserPresenceUpdated($eventData, $data['company_id'], $data['user_id']));

            Log::info('WebSocket Test: UserPresenceUpdated event triggered', $eventData);

            return $this->sendResponse($eventData, 'UserPresenceUpdated event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: UserPresenceUpdated', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger UserPresenceUpdated event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Typing Indicator Event
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testTypingIndicator(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'user_id' => 'required|integer',
                'conversation_id' => 'required|string',
                'is_typing' => 'required|boolean'
            ]);

            $eventData = array_merge([
                'user_id' => $data['user_id'],
                'conversation_id' => $data['conversation_id'],
                'is_typing' => $data['is_typing'],
                'timestamp' => now()->toISOString(),
                'test_event' => true
            ], $data);

            event(new UserTypingIndicator($eventData, $data['company_id'], $data['conversation_id']));

            Log::info('WebSocket Test: UserTypingIndicator event triggered', $eventData);

            return $this->sendResponse($eventData, 'UserTypingIndicator event triggered successfully');

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: UserTypingIndicator', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger UserTypingIndicator event', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Test Bulk Events - Trigger multiple events for comprehensive testing
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testBulkEvents(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'company_id' => 'required|string',
                'user_id' => 'required|integer',
                'count' => 'integer|min:1|max:50'
            ]);

            $count = $data['count'] ?? 10;
            $results = [];

            for ($i = 1; $i <= $count; $i++) {
                // Trigger different events in sequence
                $eventType = $i % 5;

                switch ($eventType) {
                    case 0:
                        $eventData = [
                            'service_id' => 1000 + $i,
                            'service_name' => "Test Service #{$i}",
                            'customer_name' => "Test Customer #{$i}",
                            'priority' => ['low', 'medium', 'high'][rand(0, 2)],
                            'created_by' => 'Bulk Test',
                            'test_event' => true
                        ];
                        event(new ServiceCreated($eventData, $data['company_id']));
                        $results[] = "ServiceCreated #{$i}";
                        break;

                    case 1:
                        $eventData = [
                            'user_id' => $data['user_id'],
                            'title' => "Bulk Test Notification #{$i}",
                            'message' => "This is test notification number {$i}",
                            'priority' => 'medium',
                            'test_event' => true
                        ];
                        event(new UserNotificationReceived($eventData, $data['company_id'], $data['user_id']));
                        $results[] = "UserNotification #{$i}";
                        break;

                    case 2:
                        $eventData = [
                            'payment_id' => 2000 + $i,
                            'amount' => rand(1000, 10000),
                            'customer_name' => "Test Customer #{$i}",
                            'payment_method' => 'test',
                            'test_event' => true
                        ];
                        event(new PaymentReceived($eventData, $data['company_id']));
                        $results[] = "PaymentReceived #{$i}";
                        break;

                    case 3:
                        $eventData = [
                            'user_id' => $data['user_id'],
                            'status' => ['online', 'away', 'busy'][rand(0, 2)],
                            'activity' => "Bulk Test Activity #{$i}",
                            'test_event' => true
                        ];
                        event(new UserPresenceUpdated($eventData, $data['company_id'], $data['user_id']));
                        $results[] = "UserPresence #{$i}";
                        break;

                    case 4:
                        $dashboardData = [
                            'services' => ['total' => rand(100, 500), 'completed_today' => rand(10, 50)],
                            'financial' => ['revenue_today' => rand(10000, 50000)],
                            'test_event' => true,
                            'bulk_test_iteration' => $i
                        ];
                        event(new DashboardMetricsUpdated($dashboardData, $data['company_id']));
                        $results[] = "DashboardMetrics #{$i}";
                        break;
                }

                // Small delay between events
                usleep(100000); // 0.1 second
            }

            Log::info('WebSocket Test: Bulk events triggered', [
                'company_id' => $data['company_id'],
                'count' => $count,
                'events' => $results
            ]);

            return $this->sendResponse([
                'events_triggered' => $count,
                'event_details' => $results,
                'company_id' => $data['company_id'],
                'test_completed_at' => now()->toISOString()
            ], "Successfully triggered {$count} bulk test events");

        } catch (\Exception $e) {
            Log::error('WebSocket Test Error: Bulk Events', ['error' => $e->getMessage()]);
            return $this->sendError('Failed to trigger bulk events', ['error' => $e->getMessage()]);
        }
    }
}
