<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSalesAmountToPurchaseorderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('purchaseorder', function (Blueprint $table) {
            $table->integer('sales_amount')->default(0)->after('total')->comment('Sales amount for the purchase order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('purchaseorder', function (Blueprint $table) {
            $table->dropColumn('sales_amount');
        });
    }
}
