{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "barryvdh/laravel-dompdf": "^2.1", "darkaonline/l5-swagger": "^8.5", "doctrine/dbal": "~2.3", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.18", "guzzlehttp/guzzle": "^7.0.1", "infyomlabs/adminlte-templates": "^3.0", "infyomlabs/generator-builder": "^1.0", "infyomlabs/laravel-generator": "^3.0", "infyomlabs/swagger-generator": "^2.0", "laravel/framework": "^8.75", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "laravelcollective/html": "^5.4|^5.5|^5.6|^5.8|^6.3", "league/csv": "^9.16", "league/flysystem-aws-s3-v3": "*", "maatwebsite/excel": "^3.1", "phonepe/phonepe-pg-php-sdk": "^1.0", "phpoffice/phpspreadsheet": "1.18", "ramsey/uuid": "^4.7", "spatie/laravel-medialibrary": "^9.6", "spatie/laravel-permission": "^6.3", "tymon/jwt-auth": "dev-develop", "yajra/laravel-datatables-buttons": "^4.0", "yajra/laravel-datatables-html": "^3.0|^4.0", "yajra/laravel-datatables-oracle": "^8.0 || ^9.0"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/Helper/helper.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "package", "package": [{"dist": {"type": "zip", "url": "https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-php-sdk/phonepe-pg-php-sdk.zip"}, "name": "phonepe/phonepe-pg-php-sdk", "version": "1.0.0", "autoload": {"classmap": ["/"]}}]}]}