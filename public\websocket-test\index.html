<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrackNew WebSocket Test Client</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }
        
        .panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        
        .panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: 600;
            text-align: center;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            background: white;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        
        .message.error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .message.success {
            border-left-color: #27ae60;
            background: #f0f9f4;
        }
        
        .message-time {
            color: #6c757d;
            font-size: 10px;
            margin-bottom: 3px;
        }
        
        .channel-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .channel-tag {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: #495057;
        }
        
        .channel-tag.subscribed {
            background: #d4edda;
            color: #155724;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TrackNew WebSocket Test Client</h1>
            <p>Comprehensive testing interface for real-time WebSocket functionality</p>
        </div>
        
        <div class="main-content">
            <!-- Connection Panel -->
            <div class="panel">
                <h3>🔌 Connection Management</h3>
                
                <div id="connectionStatus" class="status disconnected">
                    Disconnected
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="messagesReceived">0</div>
                        <div class="stat-label">Messages</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="channelsSubscribed">0</div>
                        <div class="stat-label">Channels</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="connectionTime">0s</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="wsUrl">WebSocket URL:</label>
                    <input type="text" id="wsUrl" value="ws://127.0.0.1:6001/app/tracknew-websocket-key?protocol=7&client=js&version=7.0.3&flash=false">
                </div>
                
                <div class="form-group">
                    <label for="jwtToken">JWT Token:</label>
                    <textarea id="jwtToken" rows="3" placeholder="Enter your JWT token here..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="companyId">Company ID:</label>
                    <input type="text" id="companyId" value="test-company-123" placeholder="Enter company ID">
                </div>
                
                <div class="form-group">
                    <label for="userId">User ID:</label>
                    <input type="number" id="userId" value="1" placeholder="Enter user ID">
                </div>
                
                <button class="btn" onclick="connect()">🔗 Connect</button>
                <button class="btn danger" onclick="disconnect()">❌ Disconnect</button>
                <button class="btn" onclick="clearMessages()">🧹 Clear Log</button>
            </div>
            
            <!-- Channel Management Panel -->
            <div class="panel">
                <h3>📡 Channel Management</h3>
                
                <div class="form-group">
                    <label for="channelType">Channel Type:</label>
                    <select id="channelType" onchange="updateChannelName()">
                        <option value="company">Company Channel</option>
                        <option value="user">User Channel</option>
                        <option value="service">Service Channel</option>
                        <option value="custom">Custom Channel</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="channelSubtype">Channel Subtype:</label>
                    <select id="channelSubtype" onchange="updateChannelName()">
                        <option value="services">Services</option>
                        <option value="dashboard">Dashboard</option>
                        <option value="payments">Payments</option>
                        <option value="notifications">Notifications</option>
                        <option value="messages">Messages</option>
                        <option value="assignments">Assignments</option>
                        <option value="updates">Updates</option>
                        <option value="comments">Comments</option>
                        <option value="tracking">Tracking</option>
                        <option value="admin.broadcasts">Admin Broadcasts</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="channelName">Channel Name:</label>
                    <input type="text" id="channelName" placeholder="Channel will be auto-generated">
                </div>
                
                <button class="btn success" onclick="subscribeToChannel()">➕ Subscribe</button>
                <button class="btn danger" onclick="unsubscribeFromChannel()">➖ Unsubscribe</button>
                
                <div class="form-group">
                    <label>Subscribed Channels:</label>
                    <div id="subscribedChannels" class="channel-list">
                        <span class="channel-tag">No channels subscribed</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Message Testing Panel -->
        <div class="panel" style="margin: 20px;">
            <h3>💬 Message Testing</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <div class="form-group">
                        <label for="testEventType">Test Event Type:</label>
                        <select id="testEventType">
                            <option value="ServiceCreated">Service Created</option>
                            <option value="ServiceStatusUpdated">Service Status Updated</option>
                            <option value="ServiceAssigned">Service Assigned</option>
                            <option value="ServiceOTPVerified">Service OTP Verified</option>
                            <option value="DashboardMetricsUpdated">Dashboard Metrics Updated</option>
                            <option value="PaymentReceived">Payment Received</option>
                            <option value="InvoiceGenerated">Invoice Generated</option>
                            <option value="AMCRenewalDue">AMC Renewal Due</option>
                            <option value="AMCServiceScheduled">AMC Service Scheduled</option>
                            <option value="UserNotificationReceived">User Notification</option>
                            <option value="MessageSent">Message Sent</option>
                            <option value="UserPresenceUpdated">User Presence Updated</option>
                            <option value="UserTypingIndicator">Typing Indicator</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="testMessage">Test Message Data:</label>
                        <textarea id="testMessage" rows="4" placeholder="JSON data for test message..."></textarea>
                    </div>
                    
                    <button class="btn" onclick="sendTestMessage()">📤 Send Test Message</button>
                    <button class="btn" onclick="generateSampleData()">🎲 Generate Sample</button>
                </div>
                
                <div>
                    <div class="form-group">
                        <label>Quick Actions:</label>
                        <button class="btn" onclick="testServiceWorkflow()">🔧 Test Service Workflow</button>
                        <button class="btn" onclick="testDashboardUpdates()">📊 Test Dashboard</button>
                        <button class="btn" onclick="testPaymentFlow()">💰 Test Payments</button>
                        <button class="btn" onclick="testCommunication()">💬 Test Communication</button>
                        <button class="btn" onclick="testPresence()">👥 Test Presence</button>
                        <button class="btn" onclick="runFullTest()">🚀 Run Full Test</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Message Log Panel -->
        <div class="panel" style="margin: 20px;">
            <h3>📋 Message Log</h3>
            <div id="messageLog" class="messages">
                <div class="message">
                    <div class="message-time">Ready to connect...</div>
                    <div>WebSocket test client initialized. Configure connection settings and click Connect.</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://js.pusher.com/7.2/pusher.min.js"></script>
    <script>
        let pusher = null;
        let subscribedChannels = new Map();
        let messageCount = 0;
        let connectionStartTime = null;
        let connectionTimer = null;

        // Initialize channel name based on selections
        function updateChannelName() {
            const channelType = document.getElementById('channelType').value;
            const channelSubtype = document.getElementById('channelSubtype').value;
            const companyId = document.getElementById('companyId').value;
            const userId = document.getElementById('userId').value;

            let channelName = '';

            switch(channelType) {
                case 'company':
                    channelName = `company.${companyId}.${channelSubtype}`;
                    break;
                case 'user':
                    channelName = `user.${userId}.${channelSubtype}`;
                    break;
                case 'service':
                    channelName = `service.123.${channelSubtype}`;
                    break;
                case 'custom':
                    channelName = '';
                    break;
            }

            document.getElementById('channelName').value = channelName;
        }

        // Connection Management
        function connect() {
            const jwtToken = document.getElementById('jwtToken').value.trim();
            if (!jwtToken) {
                addMessage('error', 'JWT Token is required for authentication');
                return;
            }

            updateConnectionStatus('connecting');
            addMessage('info', 'Attempting to connect to WebSocket server...');

            try {
                pusher = new Pusher('tracknew-websocket-key', {
                    wsHost: '127.0.0.1',
                    wsPort: 6001,
                    wssPort: 6001,
                    forceTLS: false,
                    enabledTransports: ['ws', 'wss'],
                    auth: {
                        headers: {
                            'Authorization': `Bearer ${jwtToken}`,
                            'X-Company-ID': document.getElementById('companyId').value,
                            'X-User-ID': document.getElementById('userId').value
                        }
                    },
                    authEndpoint: '/broadcasting/auth'
                });

                pusher.connection.bind('connected', function() {
                    updateConnectionStatus('connected');
                    connectionStartTime = Date.now();
                    startConnectionTimer();
                    addMessage('success', `Connected successfully! Socket ID: ${pusher.connection.socket_id}`);
                });

                pusher.connection.bind('disconnected', function() {
                    updateConnectionStatus('disconnected');
                    stopConnectionTimer();
                    addMessage('error', 'Disconnected from WebSocket server');
                });

                pusher.connection.bind('error', function(err) {
                    updateConnectionStatus('disconnected');
                    addMessage('error', `Connection error: ${JSON.stringify(err)}`);
                });

                pusher.connection.bind('state_change', function(states) {
                    addMessage('info', `Connection state changed: ${states.previous} → ${states.current}`);
                });

            } catch (error) {
                updateConnectionStatus('disconnected');
                addMessage('error', `Failed to initialize connection: ${error.message}`);
            }
        }

        function disconnect() {
            if (pusher) {
                // Unsubscribe from all channels
                subscribedChannels.forEach((channel, channelName) => {
                    pusher.unsubscribe(channelName);
                });
                subscribedChannels.clear();
                updateSubscribedChannelsList();

                pusher.disconnect();
                pusher = null;
                updateConnectionStatus('disconnected');
                stopConnectionTimer();
                addMessage('info', 'Disconnected from WebSocket server');
            }
        }

        // Channel Management
        function subscribeToChannel() {
            if (!pusher || pusher.connection.state !== 'connected') {
                addMessage('error', 'Not connected to WebSocket server');
                return;
            }

            const channelName = document.getElementById('channelName').value.trim();
            if (!channelName) {
                addMessage('error', 'Channel name is required');
                return;
            }

            if (subscribedChannels.has(channelName)) {
                addMessage('warning', `Already subscribed to channel: ${channelName}`);
                return;
            }

            try {
                const channel = pusher.subscribe(channelName);

                channel.bind('pusher:subscription_succeeded', function() {
                    subscribedChannels.set(channelName, channel);
                    updateSubscribedChannelsList();
                    addMessage('success', `Successfully subscribed to channel: ${channelName}`);
                });

                channel.bind('pusher:subscription_error', function(error) {
                    addMessage('error', `Failed to subscribe to channel ${channelName}: ${JSON.stringify(error)}`);
                });

                // Bind to all possible events
                const eventTypes = [
                    'ServiceCreated', 'ServiceStatusUpdated', 'ServiceAssigned', 'ServiceOTPVerified',
                    'DashboardMetricsUpdated', 'PaymentReceived', 'InvoiceGenerated',
                    'AMCRenewalDue', 'AMCServiceScheduled', 'UserNotificationReceived',
                    'MessageSent', 'UserPresenceUpdated', 'UserTypingIndicator'
                ];

                eventTypes.forEach(eventType => {
                    channel.bind(eventType, function(data) {
                        messageCount++;
                        updateStats();
                        addMessage('success', `📨 ${eventType} received on ${channelName}`, data);
                    });
                });

            } catch (error) {
                addMessage('error', `Failed to subscribe to channel: ${error.message}`);
            }
        }

        function unsubscribeFromChannel() {
            const channelName = document.getElementById('channelName').value.trim();
            if (!channelName) {
                addMessage('error', 'Channel name is required');
                return;
            }

            if (!subscribedChannels.has(channelName)) {
                addMessage('warning', `Not subscribed to channel: ${channelName}`);
                return;
            }

            if (pusher) {
                pusher.unsubscribe(channelName);
                subscribedChannels.delete(channelName);
                updateSubscribedChannelsList();
                addMessage('info', `Unsubscribed from channel: ${channelName}`);
            }
        }

        // UI Management Functions
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status ${status}`;

            switch(status) {
                case 'connected':
                    statusElement.textContent = '🟢 Connected';
                    break;
                case 'connecting':
                    statusElement.textContent = '🟡 Connecting...';
                    break;
                case 'disconnected':
                    statusElement.textContent = '🔴 Disconnected';
                    break;
            }
        }

        function updateSubscribedChannelsList() {
            const container = document.getElementById('subscribedChannels');

            if (subscribedChannels.size === 0) {
                container.innerHTML = '<span class="channel-tag">No channels subscribed</span>';
                return;
            }

            container.innerHTML = '';
            subscribedChannels.forEach((channel, channelName) => {
                const tag = document.createElement('span');
                tag.className = 'channel-tag subscribed';
                tag.textContent = channelName;
                tag.onclick = () => {
                    document.getElementById('channelName').value = channelName;
                };
                container.appendChild(tag);
            });
        }

        function updateStats() {
            document.getElementById('messagesReceived').textContent = messageCount;
            document.getElementById('channelsSubscribed').textContent = subscribedChannels.size;
        }

        function startConnectionTimer() {
            connectionTimer = setInterval(() => {
                if (connectionStartTime) {
                    const uptime = Math.floor((Date.now() - connectionStartTime) / 1000);
                    document.getElementById('connectionTime').textContent = `${uptime}s`;
                }
            }, 1000);
        }

        function stopConnectionTimer() {
            if (connectionTimer) {
                clearInterval(connectionTimer);
                connectionTimer = null;
            }
            document.getElementById('connectionTime').textContent = '0s';
        }

        function addMessage(type, message, data = null) {
            const messageLog = document.getElementById('messageLog');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();

            const contentDiv = document.createElement('div');
            contentDiv.textContent = message;

            messageDiv.appendChild(timeDiv);
            messageDiv.appendChild(contentDiv);

            if (data) {
                const dataDiv = document.createElement('div');
                dataDiv.style.marginTop = '5px';
                dataDiv.style.fontSize = '11px';
                dataDiv.style.color = '#6c757d';
                dataDiv.textContent = JSON.stringify(data, null, 2);
                messageDiv.appendChild(dataDiv);
            }

            messageLog.appendChild(messageDiv);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messageLog').innerHTML = '';
            messageCount = 0;
            updateStats();
            addMessage('info', 'Message log cleared');
        }

        // Test Message Functions
        function generateSampleData() {
            const eventType = document.getElementById('testEventType').value;
            let sampleData = {};

            switch(eventType) {
                case 'ServiceCreated':
                    sampleData = {
                        service_id: 123,
                        service_name: 'AC Repair Service',
                        customer_name: 'John Doe',
                        priority: 'high',
                        created_by: 'Admin User',
                        estimated_completion: '2025-01-18 14:00:00'
                    };
                    break;
                case 'ServiceStatusUpdated':
                    sampleData = {
                        service_id: 123,
                        old_status: 'pending',
                        new_status: 'in_progress',
                        updated_by: 'Technician',
                        notes: 'Started working on the AC unit'
                    };
                    break;
                case 'PaymentReceived':
                    sampleData = {
                        payment_id: 456,
                        amount: 2500.00,
                        service_id: 123,
                        customer_name: 'John Doe',
                        payment_method: 'credit_card',
                        transaction_id: 'TXN123456789'
                    };
                    break;
                case 'DashboardMetricsUpdated':
                    sampleData = {
                        total_services: 150,
                        completed_today: 12,
                        pending_services: 25,
                        revenue_today: 15000.00,
                        active_technicians: 8
                    };
                    break;
                case 'UserNotificationReceived':
                    sampleData = {
                        notification_id: 789,
                        title: 'New Service Assignment',
                        message: 'You have been assigned to service #123',
                        priority: 'high',
                        action_required: true
                    };
                    break;
                default:
                    sampleData = {
                        event_type: eventType,
                        timestamp: new Date().toISOString(),
                        test_data: true
                    };
            }

            document.getElementById('testMessage').value = JSON.stringify(sampleData, null, 2);
        }

        function sendTestMessage() {
            addMessage('info', 'Note: This is a frontend test client. To send actual messages, use the backend test endpoints.');
        }

        // Quick Test Functions
        function testServiceWorkflow() {
            addMessage('info', '🔧 Testing Service Workflow - subscribing to service channels...');

            const companyId = document.getElementById('companyId').value;
            const userId = document.getElementById('userId').value;

            const channels = [
                `company.${companyId}.services`,
                `user.${userId}.assignments`,
                `service.123.updates`
            ];

            channels.forEach(channel => {
                document.getElementById('channelName').value = channel;
                subscribeToChannel();
            });
        }

        function testDashboardUpdates() {
            addMessage('info', '📊 Testing Dashboard Updates - subscribing to dashboard channels...');

            const companyId = document.getElementById('companyId').value;
            document.getElementById('channelName').value = `company.${companyId}.dashboard`;
            subscribeToChannel();
        }

        function testPaymentFlow() {
            addMessage('info', '💰 Testing Payment Flow - subscribing to payment channels...');

            const companyId = document.getElementById('companyId').value;
            document.getElementById('channelName').value = `company.${companyId}.payments`;
            subscribeToChannel();
        }

        function testCommunication() {
            addMessage('info', '💬 Testing Communication - subscribing to message channels...');

            const userId = document.getElementById('userId').value;
            const channels = [
                `user.${userId}.notifications`,
                `user.${userId}.messages`
            ];

            channels.forEach(channel => {
                document.getElementById('channelName').value = channel;
                subscribeToChannel();
            });
        }

        function testPresence() {
            addMessage('info', '👥 Testing Presence - subscribing to presence channels...');

            const companyId = document.getElementById('companyId').value;
            document.getElementById('channelName').value = `company.${companyId}.presence`;
            subscribeToChannel();
        }

        function runFullTest() {
            addMessage('info', '🚀 Running Full Test Suite - subscribing to all channel types...');

            setTimeout(() => testServiceWorkflow(), 500);
            setTimeout(() => testDashboardUpdates(), 1000);
            setTimeout(() => testPaymentFlow(), 1500);
            setTimeout(() => testCommunication(), 2000);
            setTimeout(() => testPresence(), 2500);

            addMessage('success', 'Full test suite initiated! Check subscribed channels.');
        }

        // Initialize on page load
        updateChannelName();
        updateStats();
    </script>
</body>
</html>
