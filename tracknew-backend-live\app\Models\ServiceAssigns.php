<?php

namespace App\Models;

use Eloquent as Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @OA\Schema(
 *      schema="ServiceAssigns",
 *      required={""},
 *      @OA\Property(
 *          property="user_id",
 *          description="user_id",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="service_id",
 *          description="service_id",
 *          readOnly=false,
 *          nullable=false,
 *          type="integer",
 *          format="int32"
 *      ),
 *      @OA\Property(
 *          property="created_at",
 *          description="created_at",
 *          readOnly=false,
 *          nullable=false,
 *          type="string",
 *          format="date-time"
 *      ),
 *      @OA\Property(
 *          property="updated_at",
 *          description="updated_at",
 *          readOnly=false,
 *          nullable=false,
 *          type="string",
 *          format="date-time"
 *      )
 * )
 */
class ServiceAssigns extends Model
{
    use SoftDeletes;

    use HasFactory;

    public $table = 'service_assigns';
    

    protected $dates = ['deleted_at'];



    public $fillable = [
        'user_id',
        'service_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'service_id' => 'integer'
    ];

    /**
     * Validation rules
     *
     * @var array
     */
    public static $rules = [
        
    ];
    
    public function users()
    {
        return $this->belongsToMany(User::class, 'service_assigns', 'service_id', 'user_id');
    }  
    
    public function services()
    {
        return $this->belongsTo(Services::class, 'service_id'); // Assuming 'service_id' is the foreign key in ServiceAssigns table
    }

    
}
