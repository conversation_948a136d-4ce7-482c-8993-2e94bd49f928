<?php
use App\Http\Controllers\Api\TemplateController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\{
    LoginController,
    RegisterController,
    RoleController
};

use App\Http\Controllers\API\RolePermissionApiController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });
Route::post('auth/login',[LoginController::class,'login']);

Route::post('auth/mobile-login',[LoginController::class,'mobileLogin']);

Route::post('auth/mobile-verify',[LoginController::class, 'verifyOTP']);

Route::post('auth/register',[RegisterController::class,'register']);

Route::put('delete-account/{id}', [RegisterController::class, 'delete']);

Route::group(['prefix' => 'roles'], function () {
  
    Route::get('/', [RoleController::class, 'index']);
  
    Route::get('/{id}', [RoleController::class, 'show']);
  
    Route::post('/', [RoleController::class, 'store']);
  
    Route::put('/{id}', [RoleController::class, 'update']);
  
    Route::delete('/{id}', [RoleController::class, 'destroy']);
});

Route::get('/permissions', [RolePermissionApiController::class, 'index']);

Route::get('job-sheet/{id}', [App\Http\Controllers\API\ServicesAPIController::class, 'createPDF']);

Route::get('view-job-sheet/{id}', [App\Http\Controllers\API\ServicesAPIController::class, 'viewPDF']);

Route::get('download-invoice/{id}/{sign}', [App\Http\Controllers\API\SalesAPIController::class, 'createPDF']);

Route::get('view-invoice/{id}/{sign}', [App\Http\Controllers\API\SalesAPIController::class, 'viewPDF']);

Route::get('download-estimation/{id}/{sign}', [App\Http\Controllers\API\EstimationAPIController::class, 'downloadPDF']);

Route::get('view-estimation/{id}/{sign}', [App\Http\Controllers\API\EstimationAPIController::class, 'viewPDF']);

Route::get('download-proforma/{id}/{sign}', [App\Http\Controllers\API\ProformaAPIController::class, 'downloadPDF']);

Route::get('view-proforma/{id}/{sign}', [App\Http\Controllers\API\ProformaAPIController::class, 'viewPDF']);

Route::get('/permissions', [RolePermissionApiController::class, 'index']);

Route::get('job-sheet/{id}', [App\Http\Controllers\API\ServicesAPIController::class, 'createPDF']);

Route::get('view-job-sheet/{id}', [App\Http\Controllers\API\ServicesAPIController::class, 'viewPDF']);

Route::get('download-invoice/{id}', [App\Http\Controllers\API\SalesAPIController::class, 'createPDF']);

Route::get('view-invoice/{id}', [App\Http\Controllers\API\SalesAPIController::class, 'viewPDF']);

Route::get('download-estimation/{id}', [App\Http\Controllers\API\EstimationAPIController::class, 'downloadPDF']);

Route::get('view-estimation/{id}', [App\Http\Controllers\API\EstimationAPIController::class, 'viewPDF']);

Route::get('download-proforma/{id}', [App\Http\Controllers\API\ProformaAPIController::class, 'downloadPDF']);

Route::get('view-proforma/{id}', [App\Http\Controllers\API\ProformaAPIController::class, 'viewPDF']);

Route::get('view-inward/{id}', [App\Http\Controllers\API\RmaAPIController::class, 'viewPDF']);

Route::get('download-inward/{id}', [App\Http\Controllers\API\RmaAPIController::class, 'createPDF']);

Route::get('view-outward/{id}', [App\Http\Controllers\API\RmaAPIController::class, 'outViewPDF']);

Route::get('download-outward/{id}', [App\Http\Controllers\API\RmaAPIController::class, 'outCreatePDF']); 

	Route::post('/assign_modules/companies/{companies_id}', [App\Http\Controllers\API\UserModuleAPIController::class, 'assignModules']);

 	Route::get('/companies_modules/{comapanies_id}', [App\Http\Controllers\API\UserModuleAPIController::class, 'getCompanyModules']);

	Route::get('subscription/plans', [App\Http\Controllers\API\SubscriptionApiController::class, 'index']);	

    Route::get('subscription/log', [App\Http\Controllers\API\SubscriptionAPIController::class, 'log']);
	
 	Route::post('subscription/plan/{status}', [App\Http\Controllers\API\SubscriptionAPIController::class, 'status']); 

  Route::post('subscription_sms/{status}', [App\Http\Controllers\API\SmsSubscriptionAPIController::class, 'status']);

	Route::post('debit/{status}', [App\Http\Controllers\API\SubscriptionAPIController::class, 'recurringStatus']); 
	
	Route::get('/customer-ledger', [App\Http\Controllers\API\SalesPaymentAPIController::class, 'getCustomerLedger']);

	Route::get('/view-order-invoice/{order}', [App\Http\Controllers\API\OrderAPIController::class, 'generateInvoice']);

	Route::get('/download-order-invoice/{order}', [App\Http\Controllers\API\OrderAPIController::class, 'downloadInvoice']);

Route::group(['middleware' =>['auth:api']], function () {
  
  Route::post('auth/user-register',[RegisterController::class, 'adminRegister']);

  Route::post('logout',[LoginController::class,'logout']);
  
  Route::post('subscription/subscribe/{gatewayid}/{planid}', [App\Http\Controllers\API\SubscriptionAPIController::class, 'subscribe']);  

  Route::post('sms-subscribe/{gatewayid}/{planid}', [App\Http\Controllers\API\SmsSubscriptionAPIController::class, 'subscribe']); 
  
});

Route::post('subscription/validate-vpa/{gatewayid}', [App\Http\Controllers\API\SubscriptionAPIController::class, 'vaildateVpa']);

Route::post('validate-coupon', [App\Http\Controllers\API\SubscriptionAPIController::class, 'validateCoupon']);

Route::get('debit-init', [App\Http\Controllers\API\SubscriptionAPIController::class, 'debitInit']);

Route::get('debit-execute', [App\Http\Controllers\API\SubscriptionAPIController::class, 'debitExecute']); 
 
Route::group(['middleware' =>['auth:api', 'saas']], function () {
  
  	Route::get('plans', [App\Http\Controllers\API\PlansAPIController::class, 'index']);
    
    Route::resource('invoice-templates',TemplateController::class);
  	
    Route::get('orders', [App\Http\Controllers\API\OrderAPIController::class, 'index']);
    
    Route::get('customer-details/{id}', [App\Http\Controllers\API\CustomerAPIController::class, 'fetchCustomerDetails']);
  
    Route::resource('services', App\Http\Controllers\API\ServicesAPIController::class);

    Route::resource('amcs', App\Http\Controllers\API\AmcAPIController::class);
    Route::get('amc/seven-days-report', [App\Http\Controllers\API\AmcAPIController::class, 'sevenDaysReport']);
    
  
    Route::resource('estimations', App\Http\Controllers\API\EstimationAPIController::class);
    
    Route::resource('leads', App\Http\Controllers\API\LeadsAPIController::class);
    
    Route::post('auth/user-register',[RegisterController::class, 'adminRegister']);

    Route::get('searchs', [App\Http\Controllers\API\SearchAPIController::class, 'searchResults']);
    
    Route::resource('customers', App\Http\Controllers\API\CustomerAPIController::class); 
  
  	Route::post('/customer-imports', [App\Http\Controllers\API\CustomerAPIController::class, 'importCustomers']);
    
    Route::resource('companies', App\Http\Controllers\API\CompaniesAPIController::class);
    
    Route::put('sociallinks-update/{id}', [App\Http\Controllers\API\CompaniesAPIController::class, 'sociallinksUpdate']);
    
    Route::resource('customer-categories', App\Http\Controllers\API\CustomerCategoryAPIController::class);
    
    Route::resource('service_categories', App\Http\Controllers\API\ServiceCategoryAPIController::class);
    
    Route::resource('invoice_settings', App\Http\Controllers\API\InvoiceSettingsAPIController::class);
    
    Route::resource('expensesTypes', App\Http\Controllers\API\ExpensesTypeAPIController::class);
    
    Route::resource('expenses', App\Http\Controllers\API\ExpensesAPIController::class);
    
    Route::resource('invoices', App\Http\Controllers\API\InvoicesAPIController::class);
    
    Route::get('invoice-last-number', [App\Http\Controllers\API\InvoicesAPIController::class, 'getLastInvoiceNumber']);
    
    Route::resource('whatsapp_settings', App\Http\Controllers\API\WhatsappSettingsAPIController::class);
    
    Route::resource('sms_settings', App\Http\Controllers\API\SmsSettingAPIController::class);
    
    // Route::resource('services', App\Http\Controllers\API\ServicesAPIController::class);
    
    Route::resource('employees', App\Http\Controllers\API\EmployeeAPIController::class);
    
    Route::resource('suppliers', App\Http\Controllers\API\SupplierAPIController::class);
    
    Route::resource('warehouses', App\Http\Controllers\API\WarehouseAPIController::class);
    
    Route::resource('purchase_orders', App\Http\Controllers\API\PurchaseOrderAPIController::class);
    
    Route::resource('purchase_order_items', App\Http\Controllers\API\PurchaseOrderItemAPIController::class);
    
    Route::resource('products', App\Http\Controllers\API\ProductsAPIController::class);
    
    Route::resource('sales', App\Http\Controllers\API\SalesAPIController::class);
    
    Route::resource('sales_items', App\Http\Controllers\API\SalesItemsAPIController::class);
    
    Route::resource('sales_payments', App\Http\Controllers\API\SalesPaymentAPIController::class);
  
  	Route::delete('/sales-payments/delete-by-code/{payment_code}', [App\Http\Controllers\API\SalesPaymentAPIController::class, 'destroyByPaymentCode']);
  
  	Route::post('customer/payments', [App\Http\Controllers\API\SalesAPIController::class, 'processCustomerPayments']);
    
    Route::resource('purchase_order_payments', App\Http\Controllers\API\PurchaseOrderPaymentsAPIController::class);
  
  	Route::delete('/purchase-payments/delete-by-code/{payment_code}', [App\Http\Controllers\API\PurchaseOrderPaymentsAPIController::class, 'destroyByPaymentCode']);
  	
  	Route::post('supplier/payments', [App\Http\Controllers\API\PurchaseOrderAPIController::class, 'processSupplierPayments']);
    
    Route::resource('testes', App\Http\Controllers\API\TesteAPIController::class);
    
    Route::resource('service_datas', App\Http\Controllers\API\serviceDatasAPIController::class);
    
    Route::resource('settings', App\Http\Controllers\API\settingsAPIController::class);
    
    Route::resource('lead_types', App\Http\Controllers\API\lead_typeAPIController::class); 
    
    Route::resource('lead_statuses', App\Http\Controllers\API\lead_statusAPIController::class);
    
    Route::resource('products_barcodes', App\Http\Controllers\API\ProductsBarcodeAPIController::class);
    
    Route::resource('products_details', App\Http\Controllers\API\ProductsDetailsAPIController::class);
  	
  	Route::post('/product-imports', [App\Http\Controllers\API\ProductsDetailsAPIController::class, 'importProducts']);
    
    Route::put('stock_update/{id}', [App\Http\Controllers\API\ProductsDetailsAPIController::class, 'stockUpdate']);
    
    Route::resource('amc_users', App\Http\Controllers\API\AmcUsersAPIController::class);
  	
  	Route::delete('amc_dates', [App\Http\Controllers\API\AmcDatesAPIController::class,'deleteDates']);
    
    Route::resource('estimation_users', App\Http\Controllers\API\EstimationUsersAPIController::class);
    
    Route::resource('taxes', App\Http\Controllers\API\TaxesAPIController::class);
    
    Route::resource('brands', App\Http\Controllers\API\BrandsAPIController::class);
    
    Route::resource('categories', App\Http\Controllers\API\CategoriesAPIController::class);
    
    Route::resource('units', App\Http\Controllers\API\UnitAPIController::class);
    
    Route::resource('service_assigns', App\Http\Controllers\API\ServiceAssignsAPIController::class);
    
    Route::resource('roles', App\Http\Controllers\API\RoleController::class);
    
    #Route::get('dashboard/values',  [App\Http\Controllers\API\DashboardController::class, 'dashboard_values']);
    
    // Route::post('roles/permissions/assign', [RolePermissionApiController::class, 'assignPermissionToRole']);  
    
    // Route::post('roles/permissions/remove', [RolePermissionApiController::class, 'removePermissionFromRole']);
    
    //Route::post('image',  [App\Http\Controllers\API\ImageAPIController::class, 'store']);
  
    Route::resource('image', App\Http\Controllers\API\ImageApiController::class);
    
    Route::delete('delete-image', [App\Http\Controllers\API\ImageApiController::class, 'delete']);
    
    Route::resource('hold_invoices', App\Http\Controllers\API\HoldInvoicesAPIController::class);
    
    Route::post('profile-update',[RegisterController::class,'profileUpdate']);
    
    Route::get('dashboard-values',  [App\Http\Controllers\API\DashboardController::class, 'dashboardData']);
    
    Route::get('dashboard/chart-data', [App\Http\Controllers\API\DashboardController::class, 'showChartData']);
  
  	Route::resource('proforma_invoices', App\Http\Controllers\API\ProformaAPIController::class);
  	
  	Route::resource('rmas', App\Http\Controllers\API\RmaAPIController::class);

    Route::resource('rma_additional_products', App\Http\Controllers\API\RmaAdditionalProductsAPIController::class);

    Route::resource('rma_payments', App\Http\Controllers\API\RmaPaymentsAPIController::class);

    Route::resource('rma_items', App\Http\Controllers\API\RmaItemsAPIController::class);
    
    Route::resource('rma_accessories', App\Http\Controllers\API\RmaAccessoriesAPIController::class);
  
  	Route::resource('service_forms', App\Http\Controllers\API\ServiceFormsAPIController::class);
  
  	Route::post('services/send-otp', [App\Http\Controllers\API\ServicesAPIController::class, 'sendOTPToCustomer']);
  	
  	Route::post('services/verify-otp', [App\Http\Controllers\API\ServicesAPIController::class, 'verifyOTP']);
  
  	Route::post('send-review-sms/{id}', [App\Http\Controllers\API\CustomerAPIController::class, 'sendReviewSms']);
  	
  	Route::get('notification-lists', [App\Http\Controllers\API\NotificationAPIController::class, 'notificationList']);
     
    #Route::get('employee-dashboard-values',  [App\Http\Controllers\API\DashboardController::class, 'employeeDashboardData']);
    
    #Route::get('dashboard-sales-overview',  [App\Http\Controllers\API\DashboardController::class, 'salesOverView']);
    
    #Route::get('dashboard-customers-overview',  [App\Http\Controllers\API\DashboardController::class, 'customerOverview']);
  
  	Route::post('auth/token-register',[RegisterController::class,'tokenRegister']);
  
  	Route::get('/enquires', [App\Http\Controllers\API\EnquiresAPIController::class, 'index']); // List enquires
  
    Route::put('/enquires/{id}', [App\Http\Controllers\API\EnquiresAPIController::class, 'update']); // Update specific enquire
  
    Route::delete('/enquires/{id}', [App\Http\Controllers\API\EnquiresAPIController::class, 'destroy']); // Delete specific enquire
  
  	#Website builder modules
  	Route::get('/website-list/{company_id}', [App\Http\Controllers\API\CompanySitesAPIController::class, 'getWebsiteByCompanyId']);
  
    Route::post('/website/check-username', [App\Http\Controllers\API\CompanySitesAPIController::class, 'checkUsernameAvailability']);
  
    Route::post('/website/register', [App\Http\Controllers\API\CompanySitesAPIController::class, 'registerWebsite']);
  
    Route::post('/website-domain/update', [App\Http\Controllers\API\CompanySitesAPIController::class, 'updateDomain']);
  
    Route::post('/company-sites/save', [App\Http\Controllers\API\CompanySitesAPIController::class, 'updateOrCreateCompanySite']);
  
    Route::post('/company-sites/gallery-save', [App\Http\Controllers\API\CompanySitesAPIController::class, 'updateGallery']);  
  
  	Route::resource('company-sites', App\Http\Controllers\API\CompanySitesAPIController::class);
  	#whatsapp templates api
  	Route::resource('whatsapp_templates', App\Http\Controllers\API\OptionAPIController::class);
  
    Route::resource('company_settings', App\Http\Controllers\API\CompanySettingsAPIController::class);
  
    Route::get('/whatsapp-qrcode/{id}', [App\Http\Controllers\API\CompanySettingsAPIController::class, 'getQr']);
  
    Route::post('/whatsapp-response', [App\Http\Controllers\API\CompanySettingsAPIController::class, 'getResponse']);  
  
	Route::get('/whatsapp-disconnect/{id}', [App\Http\Controllers\API\CompanySettingsAPIController::class, 'getDisconnect']);
   //Update date time for api calling
  	Route::get('/last-updates/{id}', [App\Http\Controllers\API\UpdateTrackerAPIController::class, 'getLastUpdateTimes']);
  	
  	Route::post('/whatsapp-message', [App\Http\Controllers\API\UpdateTrackerAPIController::class, 'sendWaMessage']);
});

Route::get('service-track/{service_code}', [App\Http\Controllers\API\ServicesAPIController::class, 'getTrack']);

Route::post('/process-payments', [App\Http\Controllers\API\SalesPaymentAPIController::class, 'processPayments']);
  
Route::put('material-update/{id}', [App\Http\Controllers\API\ServicesAPIController::class, 'materialStatus']);

//Route::post('/company-sites/gallery-save', [App\Http\Controllers\API\CompanySitesAPIController::class, 'updateGallery']);

Route::resource('template_tags', App\Http\Controllers\API\TemplateTagsAPIController::class);

Route::resource('website_templates', App\Http\Controllers\API\WebsiteTemplatesAPIController::class);

Route::post('/enquires', [App\Http\Controllers\API\EnquiresAPIController::class, 'store']);

Route::get('/logs', [App\Http\Controllers\API\LogViewerAPIController::class, 'showLogs']);

Route::get('/feature-update', function () {
       // Return a response
    return response()->json([
        'success' => true,
        'data' => [
            'message' => '🎉"Now Send WhatsApp messages instantly from TrackNew and boost customer engagement!"',
            'expiry_date' => '2025-02-21',
            'status' => 0
        ]
    ]);
});



Route::resource('reminders', App\Http\Controllers\API\ReminderAPIController::class);


Route::resource('tickets', App\Http\Controllers\API\TicketsAPIController::class);

Route::resource('sms-plans', App\Http\Controllers\API\SmsplansAPIController::class);

